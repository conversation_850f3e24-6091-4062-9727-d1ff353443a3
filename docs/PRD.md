⸻

Product Requirements Document (PRD)

Project: Stripe Payment Integration Monorepo

Date: 2025-08-04
Version: 1.0

⸻

1. Overview

This project is an educational codebase demonstrating the integration of <PERSON><PERSON> in a modern full-stack monorepo using Turborepo. It covers common Stripe features on both frontend (Vite + React + Shadcn UI) and backend (NestJS), including payments, subscriptions, webhooks, and customer management.

The goal is to provide developers with a clear, maintainable, and scalable example of a payment system that can be extended for real-world production use.

⸻

2. Objectives
	•	Provide a modular monorepo with separate frontend and backend apps.
	•	Implement essential Stripe payment flows:
	•	One-time payments
	•	Subscription payments
	•	Saving payment methods
	•	Managing customers
	•	Handling refunds and cancellations
	•	Secure webhook handling
	•	Showcase secure, PCI-compliant best practices for payment processing.
	•	Provide clear documentation and easy local setup for developers.

⸻

3. Tech Stack

Frontend
	•	Vite — Fast development and build tooling
	•	React 18+ — UI framework
	•	Shadcn UI — Pre-built, accessible UI components
	•	Stripe.js + @stripe/react-stripe-js — Stripe frontend SDK
	•	TypeScript — Static typing
	•	React Query (TanStack) — API data fetching and caching
	•	Zod — Form validation

Backend
	•	NestJS — Node.js backend framework
	•	Stripe Node SDK — Backend Stripe integration
	•	TypeORM / Prisma (to be decided) — Database ORM for storing payment and customer data
	•	PostgreSQL — Database
	•	Dotenv — Environment variable management
	•	Helmet + CORS — Security middleware
	•	Swagger — API documentation

Monorepo Tooling
	•	Turborepo — Monorepo management
	•	ESLint + Prettier — Code quality
	•	Husky + Lint-staged — Pre-commit hooks
	•	Docker — Optional containerized local setup

⸻

4. Key Stripe Features to Implement

Feature	Frontend	Backend
One-time payments (Card)	Payment form with Stripe Elements	Create payment intent
Subscription billing	Plan selection UI	Create subscription
Save card for future use	Card setup form	Attach payment method to customer
Refunds	Refund button in admin UI	Process refund request
Cancel subscription	Cancel button in UI	Call Stripe API to cancel
Customer portal	Redirect button	Generate portal link
Webhooks	Display payment status updates	Listen to events & update DB
Invoice history	Display past payments	Fetch from Stripe API
Error handling	Show friendly errors	Log errors securely


⸻

5. Functional Requirements

Frontend (Vite + React + Shadcn UI)
	1.	Authentication (Optional Demo)
	•	Mock login (no real auth, just session storage for demo) or integrate Clerk/Auth0 in future.
	2.	Payment Pages
	•	Checkout Page: Select product/plan → pay via Stripe Elements.
	•	Subscription Page: Subscribe to recurring plans.
	•	Customer Portal: Manage subscriptions, payment methods, and invoices.
	3.	UI Components
	•	Payment form (Stripe Elements integration)
	•	Product list
	•	Subscription management panel
	•	Payment history table
	•	Refund button (for admin demo)
	4.	API Communication
	•	Use React Query for fetching payment and subscription status.
	5.	Form Validation
	•	Zod + React Hook Form for payment and subscription forms.

Backend (NestJS)
	1.	API Endpoints
	•	POST /payments/create-intent → Create payment intent.
	•	POST /subscriptions/create → Create a subscription.
	•	POST /subscriptions/cancel → Cancel a subscription.
	•	POST /refunds → Process a refund.
	•	GET /customers/:id → Get customer details.
	•	GET /invoices → Fetch past invoices.
	•	GET /customer-portal → Generate customer portal link.
	2.	Stripe Webhooks
	•	Listen for events:
	•	payment_intent.succeeded
	•	invoice.payment_succeeded
	•	customer.subscription.updated
	•	charge.refunded
	•	Update database with payment status.
	3.	Database Models
	•	Customer (id, email, stripeCustomerId)
	•	Payment (id, amount, status, stripePaymentIntentId)
	•	Subscription (id, planId, status, stripeSubscriptionId)
	4.	Security
	•	Validate webhook signatures.
	•	Never expose secret keys in frontend.

⸻

6. Non-Functional Requirements
	•	Scalability: Code structure supports adding new payment flows.
	•	Security: PCI DSS compliance by using Stripe’s hosted fields & tokenization.
	•	Maintainability: Clear folder structure and typed APIs.
	•	Performance: Optimized builds via Turborepo caching.
	•	Developer Experience: Single command to start full stack locally.

⸻

7. Folder Structure (Monorepo)

/stripe-monorepo
  /apps
    /frontend  (Vite + React + Shadcn UI)
    /backend   (NestJS)
  /packages
    /ui        (shared UI components)
    /types     (shared TypeScript types)
    /config    (shared config/env)
  turbo.json
  package.json
  README.md


⸻

8. Environment Variables

Backend:

STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
DATABASE_URL=

Frontend:

VITE_STRIPE_PUBLISHABLE_KEY=


⸻

9. Milestones

Milestone	Description	Est. Time
M1	Setup Turborepo, create frontend & backend apps	1 day
M2	Implement Stripe one-time payments	2 days
M3	Implement subscriptions	2 days
M4	Implement webhooks	1 day
M5	Add customer portal, payment history, refunds	2 days
M6	Final polish, docs, and deployment guide	1 day


⸻

10. Success Criteria
	•	Can run pnpm dev and have both frontend & backend working locally.
	•	Can make a test payment with Stripe test card.
	•	Subscription flow works and is visible in Stripe Dashboard.
	•	Webhooks correctly update DB.
	•	Clear documentation for new developers.

⸻